/**
 * Typing Animation - Character Bounce Effect
 * เอฟเฟกต์ตัวอักษรเด้งๆ ตอนพิมพ์
 */

class TypingAnimation {
    constructor(textareaSelector) {
        this.textarea = document.querySelector(textareaSelector);
        this.lastValue = '';
        this.lastCursorPosition = 0;
        this.animationElements = [];

        if (this.textarea) {
            this.init();
        }
    }

    init() {
        // เปิด animation โดยค่าเริ่มต้น
        this.enabled = true;

        // เพิ่ม event listeners
        this.textarea.addEventListener('input', (e) => this.handleInput(e));
        this.textarea.addEventListener('keydown', (e) => this.handleKeydown(e));

        // เก็บค่าเริ่มต้น
        this.lastValue = this.textarea.value;
        this.lastCursorPosition = this.textarea.selectionStart || 0;

        // เพิ่ม CSS สำหรับ animation container
        this.addAnimationStyles();
    }

    addAnimationStyles() {
        // เพิ่ม CSS styles สำหรับ animation
        if (!document.getElementById('typing-animation-styles')) {
            const style = document.createElement('style');
            style.id = 'typing-animation-styles';
            style.textContent = `
                .typing-animation-container {
                    position: relative;
                }

                .typing-char-bounce {
                    position: fixed;
                    pointer-events: none;
                    z-index: 1000;
                    font-family: var(--na-font-mono);
                    font-size: var(--na-text-md);
                    font-weight: 600;
                    color: var(--na-primary);
                    text-shadow: 0 0 8px var(--na-primary);
                    animation: typingBounce 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
                }

                @keyframes typingBounce {
                    0% {
                        transform: translateY(0px) scale(1);
                        opacity: 1;
                    }
                    25% {
                        transform: translateY(-12px) scale(1.2);
                        opacity: 1;
                    }
                    50% {
                        transform: translateY(-6px) scale(1.1);
                        opacity: 0.9;
                    }
                    75% {
                        transform: translateY(-2px) scale(1.05);
                        opacity: 0.7;
                    }
                    100% {
                        transform: translateY(0px) scale(1);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        }

        // ทำให้ textarea container เป็น relative position
        const container = this.textarea.parentElement;
        if (getComputedStyle(container).position === 'static') {
            container.style.position = 'relative';
        }
        container.classList.add('typing-animation-container');
    }

    handleInput(e) {
        // ตรวจสอบว่า animation เปิดอยู่หรือไม่
        if (!this.enabled) return;

        const currentValue = this.textarea.value;
        const cursorPosition = this.textarea.selectionStart;

        // หาตัวอักษรที่เพิ่มเข้ามาใหม่
        if (currentValue.length > this.lastValue.length) {
            const addedLength = currentValue.length - this.lastValue.length;

            // หาตำแหน่งที่เพิ่มตัวอักษร
            let insertPosition = cursorPosition - addedLength;

            // สร้าง animation สำหรับตัวอักษรใหม่
            for (let i = 0; i < addedLength; i++) {
                const char = currentValue[insertPosition + i];
                if (char && char !== '\n') { // ไม่ animate newline
                    this.createCharAnimation(char, insertPosition + i);
                }
            }
        }

        this.lastValue = currentValue;
        this.lastCursorPosition = cursorPosition;
    }

    handleKeydown(e) {
        // ตรวจสอบว่า animation เปิดอยู่หรือไม่
        if (!this.enabled) return;

        // จัดการกับ special keys
        if (e.key === 'Enter') {
            // สร้าง animation สำหรับ Enter (แสดงเป็น ↵)
            setTimeout(() => {
                this.createCharAnimation('↵', this.textarea.selectionStart - 1);
            }, 10);
        }
    }

    createCharAnimation(char, position) {
        // สร้าง element สำหรับ animation
        const animChar = document.createElement('span');
        animChar.textContent = char;
        animChar.className = 'typing-char-bounce';

        // คำนวณตำแหน่งของตัวอักษรใน textarea
        const coords = this.getCharacterPosition(position);

        if (coords) {
            animChar.style.left = coords.x + 'px';
            animChar.style.top = coords.y + 'px';

            // เพิ่มเข้าไปใน document
            document.body.appendChild(animChar);

            // เก็บ reference เพื่อทำความสะอาดภายหลัง
            this.animationElements.push(animChar);

            // ลบ element หลังจาก animation เสร็จ
            setTimeout(() => {
                if (animChar.parentNode) {
                    animChar.parentNode.removeChild(animChar);
                }
                // ลบ reference
                const index = this.animationElements.indexOf(animChar);
                if (index > -1) {
                    this.animationElements.splice(index, 1);
                }
            }, 800);
        }
    }

    getCharacterPosition(position) {
        try {
            // ใช้ Range API เพื่อหาตำแหน่งของตัวอักษร
            const textareaRect = this.textarea.getBoundingClientRect();
            const style = getComputedStyle(this.textarea);

            // สร้าง temporary div ที่มี style เหมือน textarea
            const temp = document.createElement('div');
            temp.style.cssText = `
                position: absolute;
                visibility: hidden;
                white-space: pre-wrap;
                word-wrap: break-word;
                font-family: ${style.fontFamily};
                font-size: ${style.fontSize};
                line-height: ${style.lineHeight};
                padding: ${style.padding};
                border: ${style.border};
                width: ${this.textarea.clientWidth}px;
                height: auto;
                overflow: hidden;
            `;

            // เพิ่มข้อความจนถึงตำแหน่งที่ต้องการ
            const textBefore = this.textarea.value.substring(0, position);
            temp.textContent = textBefore;

            // เพิ่ม span สำหรับตัวอักษรที่ต้องการหาตำแหน่ง
            const charSpan = document.createElement('span');
            charSpan.textContent = this.textarea.value[position] || '|';
            temp.appendChild(charSpan);

            document.body.appendChild(temp);

            // หาตำแหน่งของ span
            const spanRect = charSpan.getBoundingClientRect();

            document.body.removeChild(temp);

            return {
                x: spanRect.left,
                y: spanRect.top
            };
        } catch (error) {
            console.warn('Error calculating character position:', error);
            return null;
        }
    }

    // Method สำหรับเปิด/ปิด animation
    enable() {
        this.enabled = true;
    }

    disable() {
        this.enabled = false;
        // ลบ animation elements ที่เหลืออยู่
        this.cleanupAnimations();
    }

    // Method สำหรับทำความสะอาด animations
    cleanupAnimations() {
        this.animationElements.forEach(element => {
            if (element.parentNode) {
                element.parentNode.removeChild(element);
            }
        });
        this.animationElements = [];
    }

    // Method สำหรับเปลี่ยนสี animation
    setAnimationColor(color) {
        const existingStyle = document.getElementById('typing-animation-color-override');
        if (existingStyle) {
            existingStyle.remove();
        }

        const style = document.createElement('style');
        style.id = 'typing-animation-color-override';
        style.textContent = `
            .typing-char-bounce {
                color: ${color} !important;
                text-shadow: 0 0 8px ${color} !important;
            }
        `;
        document.head.appendChild(style);
    }

    // Method สำหรับเปลี่ยนความเร็ว animation
    setAnimationSpeed(speed = 'normal') {
        const speeds = {
            slow: '1.2s',
            normal: '0.8s',
            fast: '0.5s'
        };

        const duration = speeds[speed] || speeds.normal;

        const existingStyle = document.getElementById('typing-animation-speed-override');
        if (existingStyle) {
            existingStyle.remove();
        }

        const style = document.createElement('style');
        style.id = 'typing-animation-speed-override';
        style.textContent = `
            .typing-char-bounce {
                animation-duration: ${duration} !important;
            }
        `;
        document.head.appendChild(style);
    }
}

// เริ่มต้น animation เมื่อ DOM โหลดเสร็จ
document.addEventListener('DOMContentLoaded', () => {
    // รอให้ textarea โหลดเสร็จก่อน
    setTimeout(() => {
        const typingAnimation = new TypingAnimation('#editor');

        // เก็บ instance ไว้ใน window เพื่อให้สามารถควบคุมได้จากภายนอก
        window.typingAnimation = typingAnimation;

        console.log('✨ Typing animation initialized! ตัวอักษรจะเด้งๆ ตอนพิมพ์แล้ว!');

        // เพิ่มคำแนะนำการใช้งานใน console
        console.log('💡 การใช้งาน:');
        console.log('   - พิมพ์ข้อความใน textarea เพื่อดู animation');
        console.log('   - window.typingAnimation.setAnimationColor("#ff6b6b") เพื่อเปลี่ยนสี');
        console.log('   - window.typingAnimation.setAnimationSpeed("fast") เพื่อเปลี่ยนความเร็ว');
        console.log('   - window.typingAnimation.disable() เพื่อปิด animation');
        console.log('   - window.typingAnimation.enable() เพื่อเปิด animation');
    }, 100);
});

// Export สำหรับใช้ใน module system
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TypingAnimation;
}
