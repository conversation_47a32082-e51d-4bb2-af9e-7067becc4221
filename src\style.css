@import url('https://fonts.googleapis.com/css2?family=Anuphan:wght@100..700&family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');

/* na = noteapp variables -> NoteAPP variables */
/* --color = root reset.css variables
    Public variables
*/
:root {
    --na-font-primary: 'General Sans', '<PERSON><PERSON><PERSON>', sans-serif;
    --na-font-mono: "<PERSON>", "<PERSON>up<PERSON>", sans-serif;
    --na-text-xs: 11px;
    --na-text-sm: 12px;
    --na-text-base: 14px;
    --na-text-md: 15px;
    --na-text-lg: 16px;
    --na-text-xl: 18px;
    --na-text-2xl: 20px;
    --na-text-3xl: 26px;
    --na-primary: hsl(195, 10%, 70%);
    --na-primary-hover: hsl(195, 10%, 60%);
    --na-primary-active: hsl(195, 10%, 80%);
    --na-primary-text: hsl(195, 10%, 20%);
    --na-secondary: hsl(208, 7%, 35%);
    --na-secondary-hover: hsl(206, 7%, 30%);
    --na-surface: hsl(195, 5%, 20%);
    --na-surface-hover: rgba(255, 255, 255, 0.3);
    --na-surface-active: hsl(0, 0%, 25%);
    --na-background: hsla(0, 0%, 20%, 0.500);
    --na-border: rgba(255, 255, 255, 0.25);
    --na-border-light: rgba(255, 255, 255, 0.1);
    --na-text-primary: hsl(205, 15%, 80%);
    --na-text-secondary: hsl(0, 0%, 70%);
    --na-text-muted: hsl(0, 0%, 60%);
    --na-danger: rgba(255, 107, 107, 0.3);
    --na-danger-hover: rgba(255, 107, 107, 0.6);
    --na-overlay-background: hsla(210, 20%, 98%, 0.800);
    --na-success: #4caf50;
    --na-warning: #ff9800;
    --na-space-xs: 0.15rem;
    --na-space-sm: 0.25rem;
    --na-space-md: 0.5rem;
    --na-space-lg: 0.8rem;
    --na-space-xl: 1rem;
    --na-space-2xl: 1.25rem;
    --na-space-3xl: 1.5rem;
    --na-space-4xl: 1.8rem;
    --na-radius-sm: 6px;
    --na-radius-md: 8px;
    --na-radius-lg: 10px;
    --na-radius-pill: 100vmax;
    --na-shadow-sm: 0 4px 32px rgba(0, 0, 0, 0.1);
    --na-shadow-lg: 0 12px 32px rgba(0, 0, 0, 0.06), 0 24px 64px rgba(0, 0, 0, 0.07);
    --na-transition-fast: 100ms ease-in-out;
    --na-transition-normal: 300ms ease;
    --na-sidebar-width: 200px;
    --na-header-height: 60px;
    --na-tabs-responsive-height: 100px;
}

body.light-mode {
    /* From Reset.css */
    --color-background-primary: hsl(210, 17%, 98%);
    --color-background-secondary: hsl(210, 7%, 94%);
    --color-background-tertiary: hsl(200, 12%, 95%);
    --color-background-elevated: hsl(0, 0%, 100%);
    --color-surface-primary: hsl(0, 0%, 100%);
    --color-surface-secondary: hsl(210, 17%, 98%);
    --color-surface-interactive: hsl(200, 12%, 95%);
    --color-surface-disabled: hsl(216, 12%, 92%);
    --color-text-primary: hsl(225, 6%, 13%);
    --color-text-secondary: hsl(213, 5%, 39%);
    --color-text-tertiary: hsl(207, 5%, 52%);
    --color-text-disabled: hsl(210, 6%, 63%);
    --color-text-inverse: hsl(180, 6%, 20%);
    --color-border-primary: hsl(195, 10%, 59%);
    --color-border-secondary: hsl(195, 10%, 70%);
    --color-border-focus: hsl(180, 6%, 40%);
    --color-border-error: hsl(4, 71%, 50%);
    --color-interactive-primary: hsl(180, 6%, 85%);
    --color-interactive-primary-hover: hsl(180, 6%, 75%);
    --color-interactive-primary-active: hsl(180, 6%, 70%);
    --color-interactive-secondary: hsl(213, 5%, 39%);
    --color-interactive-secondary-hover: hsl(206, 6%, 25%);
    --color-links-primary: hsl(180, 6%, 20%);
    --color-links-primary-hover: hsl(180, 6%, 0%);
    --color-highlight: hsl(180, 6%, 88%);
    --color-status-error: hsl(4, 71%, 50%);
    --color-status-error-bg: hsl(5, 79%, 95%);
    --color-status-success: hsl(140, 72%, 26%);
    --color-status-success-bg: hsl(137, 39%, 93%);
    --color-status-warning: hsl(41, 100%, 49%);
    --color-status-warning-bg: hsl(46, 94%, 94%);
    --color-status-info: hsl(214, 82%, 51%);
    --color-status-info-bg: hsl(218, 92%, 95%);

    --na-primary: hsl(195, 10%, 90%);
    --na-primary-hover: hsl(195, 10%, 75%);
    --na-primary-active: hsl(195, 10%, 90%);
    --na-primary-text: hsl(195, 10%, 15%);
    --na-secondary: hsl(208, 7%, 55%);
    --na-secondary-hover: hsl(206, 7%, 50%);
    --na-surface: hsl(195, 5%, 90%);
    --na-surface-hover: hsl(195, 5%, 80%);
    --na-surface-active: hsl(195, 5%, 90%);
    --na-background: hsla(0, 0%, 98%, 1);
    --na-border: hsla(0, 0%, 0%, 0.15);
    --na-border-light: hsla(0, 0%, 0%, 0.07);
    --na-text-primary: hsl(205, 15%, 15%);
    --na-text-secondary: hsl(0, 0%, 30%);
    --na-text-muted: hsl(0, 0%, 40%);
    --na-danger: hsla(0, 100%, 85%, 0.3);
    --na-danger-hover: hsla(0, 100%, 75%, 0.6);
    --na-success: hsl(122, 39%, 50%);
    --na-warning: hsl(36, 100%, 50%);
    --na-overlay-background: hsla(210, 20%, 98%, 0.800);
    --na-overlay-background-darker: hsl(210, 20%, 98%);
}

body.dark-mode {
    /* From Reset.css */
    --color-background-primary: hsl(0, 0%, 5%);
    --color-background-secondary: hsl(0, 0%, 14%);
    --color-background-tertiary: hsl(0, 0%, 20%);
    --color-background-elevated: hsl(0, 0%, 12%);
    --color-surface-primary: hsl(0, 0%, 10%);
    --color-surface-secondary: hsl(0, 0%, 17%);
    --color-surface-interactive: hsl(0, 0%, 27%);
    --color-surface-disabled: hsl(0, 0%, 34%);
    --color-text-primary: hsl(0, 0%, 98%);
    --color-text-secondary: hsl(0, 0%, 84%);
    --color-text-tertiary: hsl(0, 0%, 65%);
    --color-text-disabled: hsl(0, 0%, 46%);
    --color-text-inverse: hsl(180, 6%, 80%);
    --color-border-primary: hsl(199, 10%, 50%);
    --color-border-secondary: hsl(215, 10%, 30%);
    --color-border-focus: hsl(215, 10%, 60%);
    --color-border-error: hsl(0, 84%, 60%);
    --color-interactive-primary: hsl(180, 6%, 15%);
    --color-interactive-primary-hover: hsl(180, 6%, 25%);
    --color-interactive-primary-active: hsl(180, 6%, 20%);
    --color-interactive-secondary: hsl(220, 9%, 46%);
    --color-interactive-secondary-hover: hsl(218, 11%, 65%);
    --color-links-primary: hsl(180, 6%, 80%);
    --color-links-primary-hover: hsl(180, 6%, 100%);
    --color-highlight: hsl(180, 5%, 20%);

    --color-status-error: hsl(0, 84%, 60%);
    --color-status-error-bg: hsl(0, 34%, 18%);
    --color-status-success: hsl(160, 84%, 39%);
    --color-status-success-bg: hsl(150, 34%, 18%);
    --color-status-warning: hsl(38, 92%, 50%);
    --color-status-warning-bg: hsl(30, 34%, 18%);
    --color-status-info: hsl(217, 91%, 60%);
    --color-status-info-bg: hsl(210, 34%, 18%);

    --na-primary: hsl(195, 10%, 70%);
    --na-primary-hover: hsl(195, 10%, 60%);
    --na-primary-active: hsl(195, 10%, 80%);
    --na-primary-text: hsl(195, 10%, 20%);
    --na-secondary: hsl(208, 7%, 35%);
    --na-secondary-hover: hsl(206, 7%, 30%);
    --na-surface: hsl(195, 5%, 20%);
    --na-surface-hover: rgba(255, 255, 255, 0.3);
    --na-surface-active: hsl(0, 0%, 25%);
    --na-background: hsla(0, 0%, 20%, 0.500);
    --na-border: rgba(255, 255, 255, 0.25);
    --na-border-light: rgba(255, 255, 255, 0.1);
    --na-text-primary: hsl(205, 15%, 80%);
    --na-text-secondary: hsl(0, 0%, 70%);
    --na-text-muted: hsl(0, 0%, 60%);
    --na-danger: rgba(255, 107, 107, 0.3);
    --na-danger-hover: rgba(255, 107, 107, 0.6);
    --na-success: #4caf50;
    --na-warning: #ff9800;
    --na-overlay-background: hsla(0, 0%, 8%, 0.7);
    --na-overlay-background-darker: hsl(0, 0%, 14%);
}

@media (prefers-color-scheme: light) {
    :root {
        /* Primary */
        --na-primary: hsl(195, 10%, 90%);
        --na-primary-hover: hsl(195, 10%, 75%);
        --na-primary-active: hsl(195, 10%, 90%);
        --na-primary-text: hsl(195, 10%, 15%);

        /* Secondary */
        --na-secondary: hsl(208, 7%, 55%);
        --na-secondary-hover: hsl(206, 7%, 50%);

        /* Surface & Background */
        --na-surface: hsl(195, 5%, 90%);
        --na-surface-hover: hsl(195, 5%, 80%);
        --na-surface-active: hsl(195, 5%, 90%);
        --na-background: hsla(0, 0%, 98%, 1);

        /* Borders */
        --na-border: hsla(0, 0%, 0%, 0.15);
        --na-border-light: hsla(0, 0%, 0%, 0.07);

        /* Text */
        --na-text-primary: hsl(205, 15%, 15%);
        --na-text-secondary: hsl(0, 0%, 30%);
        --na-text-muted: hsl(0, 0%, 40%);

        /* Status */
        --na-danger: hsla(0, 100%, 85%, 0.3);
        --na-danger-hover: hsla(0, 100%, 75%, 0.6);
        --na-success: hsl(122, 39%, 50%);
        --na-warning: hsl(36, 100%, 50%);

        --na-overlay-background: hsla(210, 20%, 98%, 0.800);
        --na-overlay-background-darker: hsl(210, 20%, 98%);
    }
}

.theme-toggle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.1rem var(--na-space-lg) !important;
    background-color: transparent;
    color: var(--color-text-primary);
    font-weight: 500;
    font-size: 14px;
    font-family: "Inter", sans-serif;
    letter-spacing: -0.2px;
    border-radius: var(--na-radius-md);
    margin-bottom: var(--na-space-sm);
    transition: all 300ms cubic-bezier(0.165, 0.84, 0.44, 1);
}

.theme-toggle:hover {
    background-color: var(--na-primary);
    color: var(--na-primary-text);
    box-shadow: var(--na-shadow-sm);
}

.theme-toggle-texts {
    display: flex;
    align-items: center;
    gap: 7px;
    right: 7px;
    position: relative;
}

.theme-toggle-texts:hover {
    cursor: pointer;
}

.theme-switch {
    display: none;
}

.theme-switch-label {
    width: 40px;
    height: 20px;
    background-color: var(--na-surface-active);
    border: var(--na-border) solid 1.25px;
    border-radius: 15px;
    position: relative;
    cursor: pointer;
    transition: background-color 0.3s;
    transform: translateY(7px);
}

.theme-switch-label::before {
    content: '';
    position: absolute;
    top: 1px;
    left: 2px;
    width: 16px;
    height: 16px;
    background-color: var(--na-primary);
    border-radius: 50%;
    transition: transform 0.3s;
}

.theme-switch:checked+.theme-switch-label {
    background-color: var(--na-primary);
}

.theme-switch:checked+.theme-switch-label::before {
    transform: translateX(18px);
    background-color: var(--na-surface-active);
}

body.light-mode .theme-switch-label::before {
    background-color: #a5a5a5 !important;
}

/* Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--na-font-primary) !important;
    height: 100vh;
    overflow: hidden;
}

/* Layout */
.app-container {
    display: flex;
    height: 100vh;
    backdrop-filter: blur(20px);
}

#background-blur {
    position: fixed;
    left: 50%;
    top: 50%;
    z-index: 99;
    background-color: var(--na-overlay-background);
    transform: translate(-50%, -50%);
    -webkit-backdrop-filter: blur(7px);
    backdrop-filter: blur(7px);
    transition: 400ms cubic-bezier(0.19, 1, 0.22, 1);
    width: 100%;
    height: 100vh;

    opacity: 0;
    visibility: hidden;
}

.sidebar {
    width: var(--na-sidebar-width);
    height: 100vh;
    backdrop-filter: blur(40px);
    border-right: 1px solid var(--na-border);
    display: flex;
    flex-direction: column;
    transition: all var(--na-transition-normal);
    position: fixed;
    z-index: 100;
    background-color: var(--na-background);
    transform: translateX(-260px);
}

.sidebar-header {
    padding: var(--na-space-xl) 0.7rem;
    border-bottom: 1px solid var(--na-border-light);
    padding-bottom: 0.65rem;
}

.sidebar-logo {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: var(--na-space-xl);
    justify-content: space-between;
    padding: 0 var(--na-space-md);
}

.logo {
    font-size: var(--na-text-xl);
    font-weight: 550;
    display: flex;
    align-items: center;
    gap: 10px;
    letter-spacing: -0.6px;
}

#toggleSidebar {
    margin-left: var(--na-space-md);
    padding: 0.3rem;
    cursor: pointer;
    width: 34px;
    height: auto;
    border-radius: var(--na-radius-sm);
    transform: translateX(7px);
    transition: all var(--na-transition-fast);
}

#toggleSidebar:hover {
    color: hsl(0, 0%, 80%);
    background-color: hsla(0, 0%, 100%, 0.2);
}

.sidebar-logo svg {
    transform: translateY(1.5px);
    transition: all var(--na-transition-fast);
}

.sidebar-logo svg:hover {
    color: hsl(195, 10%, 75%);
}

.bottom-sidebar {
    position: fixed;
    bottom: 10px;
    min-width: max-content;
    width: 100%;
    left: 0;
    padding: var(--na-space-lg) var(--na-space-xl);
}

#localstr-moniter {
    display: flex;
    flex-direction: column;
    gap: 6px;
    width: 100%;
    min-width: 100%;
    max-width: 100%;
}

#localstr-moniter span {
    font-weight: 500;
    letter-spacing: -0.4px;
    margin-bottom: 0.2rem;
    display: inline-block;
    opacity: 80%;
}

#bar-bg {
    width: 100%;
    height: 8px;
    background: var(--na-surface-active);
    outline: var(--na-border) solid 1.25px;
    border-radius: 100vmax;
    overflow: hidden;
}

#bar-fill {
    height: 100%;
    background: var(--na-primary);
    width: 0%;
    transition: width 0.3s ease;
}

#moniterDisplay {
    font-weight: 400;
    font-size: 14px;
    margin-top: 0.2rem;
    opacity: 70%;
    font-family: "Inter", sans-serif;
}

#btn-clear {
    background: transparent;
    border: 1px solid #555;
    border-radius: 5px;
    color: #bbb;
    padding: 4px 0;
    cursor: pointer;
    font-size: 11px;
    transition: background 0.2s ease;
}

#btn-clear:hover {
    background: #0077b6;
    color: white;
    border-color: #0077b6;
}

/* Button System */
.btn {
    padding: var(--na-space-md) 10px;
    border: none;
    border-radius: var(--na-radius-md);
    background: var(--na-surface);
    cursor: pointer;
    font-weight: 600;
    letter-spacing: -0.2px;
    transition: all var(--na-transition-normal);
    display: inline-block;
    text-align: left;
    width: 100%;
    font-size: var(--na-text-base);
}

#saveBtn,
#exportBtn {
    padding: var(--na-space-md) 1.05rem;
    border-radius: var(--na-radius-pill);
    box-sizing: var(--na-shadow-sm);
}

.btn:hover {
    background: var(--na-surface-hover);
}

.btn-primary {
    background: var(--na-primary);
    color: var(--na-primary-text);
    display: block;
    width: 100%;
}

.btn-primary:hover {
    background: var(--na-primary-hover);
    color: hsl(195, 10%, 10%);
}

.btn-primary:active {
    background: var(--na-primary-active);
    color: hsl(195, 10%, 30%);
}

.filesBtn {
    background-color: transparent;
}

.filesBtn:hover {
    background: hsl(195, 2%, 25%);
    color: hsl(195, 10%, 80%);
}

.btn-secondary {
    background: var(--na-secondary);
}

.btn-secondary:hover {
    background: var(--na-secondary-hover);
}

.btnModel {
    width: fit-content;
    padding: 0.45rem 1.25rem;
    border-radius: var(--na-radius-pill);
    font-size: var(--na-text-lg);
}

/* File System */

.files-section-label {
    padding: var(--na-space-lg) 1.2rem;
    padding-bottom: 0.15rem;
    font-size: 14px;
    font-family: "Inter", sans-serif;
}

.sidebar .file-list {
    padding: var(--na-space-xl) 0.7rem;
    padding-top: var(--na-space-sm);
}

.file-list-tabs {
    display: flex;
    gap: 10px;
}

.tabsFile {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    height: var(--na-header-height);
    overflow: hidden;
}

#createFileBtn,
#exportFileBtn {
    padding: 0.3rem var(--na-space-lg) !important;
    margin-bottom: var(--na-space-sm);
    background-color: transparent;
    color: var(--color-text-primary);
    padding: 0.45rem var(--na-space-md);
    display: flex;
    align-items: center;
    justify-content: start;
    font-weight: 500;
    font-family: "Inter", sans-serif;
    font-size: 14px;
}

#exportFileBtn {
    display: none;
}

#createFileBtn svg,
#exportFileBtn svg {
    position: relative;
    right: 4.5px;
    /* transform: translate(-5px, -0px); */
    margin-left: -0.3rem;
}

#exportFileBtn svg {
    right: 4px;
    margin-right: 0.1rem;
}

#createFileBtn:hover,
#exportFileBtn:hover {
    background-color: var(--na-primary);
    color: var(--na-primary-text);
    box-shadow: var(--na-shadow-sm);
}

.file-section {
    margin-bottom: 25px;
}

#FirstFilesSec {
    margin-top: var(--na-space-3xl);
}

.files-section .file-item {
    max-width: 100%;
    width: 100%;
    min-width: 100%;
    background-color: transparent;
    margin-bottom: 0.5rem;
    margin-top: 0.5rem;
    padding: var(--na-space-xs) var(--na-space-md);
}

.files-section .file-item.active {
    max-width: 100%;
    width: 100%;
    min-width: 100%;
    padding: var(--na-space-xs) var(--na-space-md);
    background-color: var(--na-primary);
    color: var(--na-primary-text);
    box-shadow: var(--na-shadow-lg);
}

.file-item {
    flex: 1;
    min-width: 80px;
    max-width: 220px;
    padding: var(--na-space-xs) var(--na-space-lg);
    padding-right: var(--na-space-lg);
    height: 32px;
    margin: 0.9rem 0;
    background: var(--na-surface);
    color: var(--na-text-primary);
    border-radius: var(--na-radius-md);
    cursor: pointer;
    transition: all var(--na-transition-normal);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    overflow: hidden;
    gap: 25px;
    white-space: nowrap;
}

.file-item:hover,
.file-item:focus {
    background: hsla(0, 0%, 100%, 0.2);
}

.file-item.active {
    background-color: var(--na-surface-active);
    color: var(--na-text-primary);
    box-shadow: var(--na-shadow-sm);
}

.file-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
    min-width: 0;
}

.file-name {
    font-weight: 600;
    letter-spacing: -0.3px;
    font-size: var(--na-text-base);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-family: "Inter", sans-serif;
}

.file-meta {
    font-size: var(--na-text-xs);
    opacity: 0.8;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.file-actions {
    display: flex;
    gap: 0px;
    opacity: 0;
    transition: opacity var(--na-transition-normal);
    flex-shrink: 0;
}

.file-item:hover .file-actions {
    opacity: 1;
}

.action-btn {
    all: unset;
    height: fit-content;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    padding: 0rem 0.2rem;
    width: 30px !important;
    height: 30px !important;
    border-radius: var(--na-radius-md);
}

.action-btn:hover {
    color: var(--na-text-primary);
}

.close-btn {
    background: transparent;
    border: none;
    color: var(--na-text-primary);
    cursor: pointer;
    padding: 0.2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.close-btn:hover {
    background: hsla(0, 0%, 100%, 0.2);
}

.close-btn svg {
    width: 16px;
    height: 16px;
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.05);
}

.editor-header {
    padding: 0rem 20px;
    min-width: 1000px;
    margin: auto;
}

.editerDoing {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: var(--na-header-height);
}

.file-title {
    font-size: var(--na-text-xl);
    font-weight: 600;
    flex: 1;
}

.editor-actions {
    display: flex;
    gap: 10px;
    transform: translateX(8px);
    max-width: fit-content;
    min-width: max-content;
}

.editerOperator {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.editor-container {
    flex: 1;
    position: relative;
    min-width: 1000px;
    margin: auto;
}

.editor {
    width: 100%;
    height: 100%;
    background: transparent;
    border: none;
    padding: var(--na-space-xl) 25px;
    font-size: var(--na-text-md);
    line-height: 1.7;
    resize: none;
    outline: none;
    transition: all var(--na-transition-normal);
    box-shadow: none;
    font-weight: 450;
    resize: none;
    white-space: pre;
    overflow-wrap: normal;
    font-family: var(--na-font-mono);
    font-optical-sizing: auto;
    font-style: normal;
    scroll-behavior: smooth;
    overflow-y: auto;
    overflow-x: auto;
}

/* Status Bar */
.status-bar {
    padding: 12px 26px;
    min-width: 1000px;
    margin: auto;
    font-size: var(--na-text-sm);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.status-info {
    display: flex;
    gap: 20px;
}

/* Modal System */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--na-overlay-background);
    backdrop-filter: blur(10px);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: hsl(195, 2%, 10%);
    padding: var(--na-space-4xl);
    border-radius: var(--na-radius-md);
    max-width: 380px;
    min-width: 380px;
    box-shadow: var(--na-shadow-lg);
    backdrop-filter: blur(20px);
    height: 400px;
    position: relative;
    animation: fadeIn var(--na-transition-normal);
}

.modal h3 {
    margin-bottom: 20px;
    color: hsl(0, 0%, 100%);
    font-size: var(--na-text-3xl);
    letter-spacing: -0.6px;
}

.modal p {
    color: var(--na-text-secondary);
    font-weight: 450;
    margin-bottom: var(--na-space-2xl);
}

.input-group {
    margin-top: 1.65rem;
}

.input-group label {
    display: block;
    margin-bottom: var(--na-space-md);
    color: var(--na-text-muted);
}

.input-group input {
    width: 100%;
    padding: var(--na-space-lg) 0rem;
    border: none;
    border-bottom: 1px solid hsl(195, 10%, 50%);
    font-size: var(--na-text-md);
    transition: all var(--na-transition-normal);
    border-radius: 0px;
    background: transparent;
}

.input-group input::placeholder {
    color: hsl(195, 10%, 70%);
}

.input-group input:focus {
    outline: none;
    border-radius: 0px;
    border: none;
    border-bottom: 1px solid hsl(195, 10%, 70%);
}

.modal-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    position: absolute;
    bottom: 30px;
    right: 30px;
}

/* Save Indicators */
.save-indicator {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    background: rgba(76, 175, 80, 0.2);
    border-radius: 20px;
    font-size: var(--na-text-sm);
    color: var(--na-success);
}

.unsaved-indicator {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    background: rgba(255, 152, 0, 0.2);
    border-radius: 20px;
    font-size: var(--na-text-sm);
    color: var(--na-warning);
}

.indicator-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.save-indicator .indicator-dot {
    background: var(--na-success);
}

.unsaved-indicator .indicator-dot {
    background: var(--na-warning);
}

#fileTitle {
    display: none;
    font-size: 16px;
    letter-spacing: -0.4px;
    font-weight: 500;
    margin-top: 0.15rem;
    text-overflow: ellipsis;
}

/* Animations */
@keyframes slideIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }

    to {
        opacity: 1;
        transform: translateX(0px);
    }
}

.file-item {
    animation: slideIn var(--na-transition-normal);
}

.modal-content {
    animation: fadeIn var(--na-transition-normal);
}

/* Scrollbar Styles */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

.editor::-webkit-scrollbar {
    width: 14px;
    height: 14px;
}

.editor::-webkit-scrollbar-track {
    background: var(--na-surface);
    border-radius: var(--na-radius-sm);
    border: 1px solid var(--na-border-light);
}

.editor::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg,
        var(--na-primary) 0%,
        var(--na-primary-hover) 100%);
    border-radius: var(--na-radius-sm);
    border: 2px solid var(--na-surface);
    transition: all var(--na-transition-fast);
}

.editor::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg,
        var(--na-primary-hover) 0%,
        var(--na-primary-active) 100%);
    transform: scale(1.1);
}

.editor::-webkit-scrollbar-corner {
    background: var(--na-surface);
}

/* Enhanced scrollbar for light mode */
body.light-mode .editor::-webkit-scrollbar-track {
    background: var(--na-surface);
    border: 1px solid var(--na-border-light);
}

body.light-mode .editor::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg,
        rgba(0, 123, 191, 0.6) 0%,
        rgba(0, 123, 191, 0.8) 100%);
}

body.light-mode .editor::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg,
        rgba(0, 123, 191, 0.8) 0%,
        rgba(0, 123, 191, 1) 100%);
}

/* Responsive Design */

@media (max-width: 1100px) {

    .editor-header {
        max-width: 100%;
        min-width: 100%;
        margin: inherit;
    }

    .editor-container {
        max-width: 100%;
        min-width: 100%;
    }

    .status-bar {
        min-width: 100%;
        max-width: 100%;
    }

    .file-item:hover .file-actions {
        opacity: 0;
    }

    .file-item.actions-visible .file-actions,
    .file-item:focus .file-actions {
        opacity: 1;
    }

}

@media (max-width: 800px) {

    .editerDoing {
        flex-direction: column;
        align-items: inherit;
    }

    .editor-actions {
        transform: translateX(0px);
    }

    .editor-header {
        height: 120px;
    }

    .editerDoing {
        height: 85px;
    }

    .editerOperator {
        transform: translateY(var(--na-space-2xl));
    }

    .tabsFile {
        margin-top: 1rem;
    }

    #exportFileBtn {
        display: flex;
    }


}

@media (max-width: 380px) {
    .tabsFile {
        display: none;
    }

    .status-bar {
        display: none;
    }

    .editor-header {
        height: var(--na-header-height);
    }

    .editor-actions {
        position: fixed;
        right: 25px;
    }

    .btn {
        font-size: 12px;
    }

    #saveBtn {
        padding: var(--na-space-md) 1rem;
    }

    #exportBtn {
        display: none;
    }

    #fileTitle {
        display: block;
    }

    .editor {
        font-size: 12.5px;
        margin-top: 0rem;
        padding: var(--na-space-2xl) 20px;
    }

    #createFileBtn svg {
        transform: translate(-5px, -1px);
    }

    #exportFileBtn svg {
        transform: translate(-6px, 6.5px);
    }

    #exportFileBtn {
        display: flex;
    }

    #toggleSidebar {
        transform: translateX(12px);
    }

    .modal {
        background: hsl(195, 2%, 10%);
    }

    .modal-content {
        box-shadow: none;
    }

}

.intro-load {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: hsl(195, 2%, 10%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 250ms ease-out;
}

.intro-load.hidden {
    opacity: 0;
    pointer-events: none;
}

.spinner {
    border: 5px solid rgba(255, 255, 255, 0.3);
    border-top: 5px solid var(--na-primary);
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Context Menu when hold click */

#content-preview {
    position: fixed;
    z-index: 1001;
    visibility: hidden;
    opacity: 0;
    transform: scale(0.98);
    transition: opacity 0.15s ease-out, transform 0.15s ease-out;
    user-select: none;
}

#content-preview .file-item {
    box-shadow: var(--na-shadow-lg);
}

#context-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;
    background-color: var(--na-overlay-background);
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
    visibility: hidden;
    opacity: 0;
    transition: opacity 0.2s ease-out;
    user-select: none;
}

#file-context-menu {
    position: fixed;
    background: var(--na-overlay-background-darker);
    box-shadow: var(--na-shadow-lg);
    width: 200px;
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.2s ease;
    pointer-events: none;
    z-index: 1000;
    border-radius: var(--na-radius-md);
    color: var(--color-text-primary);
    user-select: none;
}

#file-context-menu .context-menu-item {
    padding: var(--na-space-md) var(--na-space-xl);
    border-bottom: 1px solid var(--na-border-light);
    cursor: pointer;
}

#file-context-menu .context-menu-item:last-child {
    border-bottom: none;
}

#file-context-menu .context-menu-item:hover {
    background: var(--na-surface-hover);
}

/* ===== SMOOTH TEXT SELECTION ANIMATION ===== */

/* Base text selection styles */
::selection {
    background: linear-gradient(135deg,
        rgba(0, 180, 216, 0.3) 0%,
        rgba(144, 224, 239, 0.4) 50%,
        rgba(0, 180, 216, 0.3) 100%);
    color: var(--na-text-primary);
    text-shadow: 0 0 8px rgba(0, 180, 216, 0.3);
    animation: selectionPulse 1.5s ease-in-out infinite alternate;
}

::-moz-selection {
    background: linear-gradient(135deg,
        rgba(0, 180, 216, 0.3) 0%,
        rgba(144, 224, 239, 0.4) 50%,
        rgba(0, 180, 216, 0.3) 100%);
    color: var(--na-text-primary);
    text-shadow: 0 0 8px rgba(0, 180, 216, 0.3);
}

/* Enhanced selection for editor textarea */
.editor::selection {
    background: linear-gradient(135deg,
        rgba(0, 180, 216, 0.4) 0%,
        rgba(144, 224, 239, 0.5) 30%,
        rgba(72, 202, 228, 0.45) 60%,
        rgba(0, 180, 216, 0.4) 100%);
    color: var(--color-text-primary);
    text-shadow: 0 0 12px rgba(0, 180, 216, 0.4);
    animation: editorSelectionGlow 2s ease-in-out infinite alternate;
}

.editor::-moz-selection {
    background: linear-gradient(135deg,
        rgba(0, 180, 216, 0.4) 0%,
        rgba(144, 224, 239, 0.5) 30%,
        rgba(72, 202, 228, 0.45) 60%,
        rgba(0, 180, 216, 0.4) 100%);
    color: var(--color-text-primary);
    text-shadow: 0 0 12px rgba(0, 180, 216, 0.4);
}

/* Light mode selection styles */
body.light-mode ::selection {
    background: linear-gradient(135deg,
        rgba(0, 123, 191, 0.25) 0%,
        rgba(100, 181, 246, 0.35) 50%,
        rgba(0, 123, 191, 0.25) 100%);
    color: var(--na-text-primary);
    text-shadow: 0 0 6px rgba(0, 123, 191, 0.2);
}

body.light-mode ::-moz-selection {
    background: linear-gradient(135deg,
        rgba(0, 123, 191, 0.25) 0%,
        rgba(100, 181, 246, 0.35) 50%,
        rgba(0, 123, 191, 0.25) 100%);
    color: var(--na-text-primary);
    text-shadow: 0 0 6px rgba(0, 123, 191, 0.2);
}

body.light-mode .editor::selection {
    background: linear-gradient(135deg,
        rgba(0, 123, 191, 0.3) 0%,
        rgba(100, 181, 246, 0.4) 30%,
        rgba(33, 150, 243, 0.35) 60%,
        rgba(0, 123, 191, 0.3) 100%);
    color: var(--color-text-primary);
    text-shadow: 0 0 8px rgba(0, 123, 191, 0.25);
}

body.light-mode .editor::-moz-selection {
    background: linear-gradient(135deg,
        rgba(0, 123, 191, 0.3) 0%,
        rgba(100, 181, 246, 0.4) 30%,
        rgba(33, 150, 243, 0.35) 60%,
        rgba(0, 123, 191, 0.3) 100%);
    color: var(--color-text-primary);
    text-shadow: 0 0 8px rgba(0, 123, 191, 0.25);
}

/* Selection animations */
@keyframes selectionPulse {
    0% {
        text-shadow: 0 0 8px rgba(0, 180, 216, 0.3);
    }
    100% {
        text-shadow: 0 0 15px rgba(0, 180, 216, 0.5);
    }
}

@keyframes editorSelectionGlow {
    0% {
        text-shadow: 0 0 12px rgba(0, 180, 216, 0.4);
    }
    100% {
        text-shadow: 0 0 20px rgba(0, 180, 216, 0.6);
    }
}

/* Smooth selection transition for all text elements */
* {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced selection for specific elements */
.file-name::selection,
.logo::selection,
h1::selection,
h2::selection,
h3::selection {
    background: linear-gradient(135deg,
        rgba(0, 180, 216, 0.35) 0%,
        rgba(144, 224, 239, 0.45) 50%,
        rgba(0, 180, 216, 0.35) 100%);
    text-shadow: 0 0 10px rgba(0, 180, 216, 0.4);
}

/* Sidebar text selection */
.sidebar ::selection {
    background: linear-gradient(135deg,
        rgba(0, 180, 216, 0.25) 0%,
        rgba(144, 224, 239, 0.35) 50%,
        rgba(0, 180, 216, 0.25) 100%);
    text-shadow: 0 0 6px rgba(0, 180, 216, 0.3);
}

.sidebar ::-moz-selection {
    background: linear-gradient(135deg,
        rgba(0, 180, 216, 0.25) 0%,
        rgba(144, 224, 239, 0.35) 50%,
        rgba(0, 180, 216, 0.25) 100%);
    text-shadow: 0 0 6px rgba(0, 180, 216, 0.3);
}

/* Button text selection */
.btn::selection {
    background: linear-gradient(135deg,
        rgba(0, 180, 216, 0.2) 0%,
        rgba(144, 224, 239, 0.3) 50%,
        rgba(0, 180, 216, 0.2) 100%);
    text-shadow: 0 0 5px rgba(0, 180, 216, 0.25);
}

.btn::-moz-selection {
    background: linear-gradient(135deg,
        rgba(0, 180, 216, 0.2) 0%,
        rgba(144, 224, 239, 0.3) 50%,
        rgba(0, 180, 216, 0.2) 100%);
    text-shadow: 0 0 5px rgba(0, 180, 216, 0.25);
}

/* ===== ENHANCED SELECTION INTERACTIONS ===== */

/* Smooth selection transition */
* {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: text;
    -khtml-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
}

/* Disable selection for UI elements */
button, .btn, .action-btn, .close-btn, svg, .sidebar-logo svg {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* Enhanced selection for code/monospace text */
.editor, code, pre {
    font-variant-ligatures: none;
    -webkit-font-feature-settings: "liga" 0;
    font-feature-settings: "liga" 0;
}

/* Selection highlight animation */
.has-selection {
    --selection-intensity: 1.2;
}

.has-selection ::selection {
    animation: selectionHighlight 0.3s ease-out;
}

.has-selection ::-moz-selection {
    animation: selectionHighlight 0.3s ease-out;
}

@keyframes selectionHighlight {
    0% {
        background-size: 100% 100%;
        opacity: 0.6;
    }
    50% {
        background-size: 110% 110%;
        opacity: 0.9;
    }
    100% {
        background-size: 100% 100%;
        opacity: 0.8;
    }
}

/* Gradient selection for different text types */
h1::selection, h2::selection, h3::selection, .logo::selection {
    background: linear-gradient(135deg,
        rgba(0, 180, 216, 0.4) 0%,
        rgba(144, 224, 239, 0.5) 25%,
        rgba(72, 202, 228, 0.45) 50%,
        rgba(144, 224, 239, 0.5) 75%,
        rgba(0, 180, 216, 0.4) 100%);
    background-size: 200% 200%;
    animation: gradientShift 3s ease-in-out infinite;
}

h1::-moz-selection, h2::-moz-selection, h3::-moz-selection, .logo::-moz-selection {
    background: linear-gradient(135deg,
        rgba(0, 180, 216, 0.4) 0%,
        rgba(144, 224, 239, 0.5) 25%,
        rgba(72, 202, 228, 0.45) 50%,
        rgba(144, 224, 239, 0.5) 75%,
        rgba(0, 180, 216, 0.4) 100%);
}

@keyframes gradientShift {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

/* Special selection for file names */
.file-name::selection {
    background: linear-gradient(135deg,
        rgba(0, 180, 216, 0.35) 0%,
        rgba(144, 224, 239, 0.45) 50%,
        rgba(0, 180, 216, 0.35) 100%);
    text-shadow: 0 0 10px rgba(0, 180, 216, 0.4);
    border-radius: 2px;
}

.file-name::-moz-selection {
    background: linear-gradient(135deg,
        rgba(0, 180, 216, 0.35) 0%,
        rgba(144, 224, 239, 0.45) 50%,
        rgba(0, 180, 216, 0.35) 100%);
    text-shadow: 0 0 10px rgba(0, 180, 216, 0.4);
}

/* Smooth cursor animation */
.editor {
    caret-color: rgba(0, 180, 216, 0.8);
    animation: caretBlink 1.2s ease-in-out infinite;
}

@keyframes caretBlink {
    0%, 50% {
        caret-color: rgba(0, 180, 216, 0.8);
    }
    51%, 100% {
        caret-color: rgba(0, 180, 216, 0.4);
    }
}

/* Selection ripple effect container */
.selection-ripple {
    position: absolute;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(0, 180, 216, 0.3) 0%, transparent 70%);
    pointer-events: none;
    z-index: 1000;
}

/* Enhanced selection for input fields */
input[type="text"]::selection,
input[type="search"]::selection,
textarea::selection {
    background: linear-gradient(135deg,
        rgba(0, 180, 216, 0.3) 0%,
        rgba(144, 224, 239, 0.4) 50%,
        rgba(0, 180, 216, 0.3) 100%);
    text-shadow: 0 0 8px rgba(0, 180, 216, 0.3);
}

input[type="text"]::-moz-selection,
input[type="search"]::-moz-selection,
textarea::-moz-selection {
    background: linear-gradient(135deg,
        rgba(0, 180, 216, 0.3) 0%,
        rgba(144, 224, 239, 0.4) 50%,
        rgba(0, 180, 216, 0.3) 100%);
    text-shadow: 0 0 8px rgba(0, 180, 216, 0.3);
}

/* Light mode adjustments for enhanced selections */
body.light-mode h1::selection,
body.light-mode h2::selection,
body.light-mode h3::selection,
body.light-mode .logo::selection {
    background: linear-gradient(135deg,
        rgba(0, 123, 191, 0.3) 0%,
        rgba(100, 181, 246, 0.4) 25%,
        rgba(33, 150, 243, 0.35) 50%,
        rgba(100, 181, 246, 0.4) 75%,
        rgba(0, 123, 191, 0.3) 100%);
}

body.light-mode .file-name::selection {
    background: linear-gradient(135deg,
        rgba(0, 123, 191, 0.25) 0%,
        rgba(100, 181, 246, 0.35) 50%,
        rgba(0, 123, 191, 0.25) 100%);
    text-shadow: 0 0 8px rgba(0, 123, 191, 0.2);
}

body.light-mode .editor {
    caret-color: rgba(0, 123, 191, 0.8);
}

/* ===== CURSOR TRACKING & SCROLL ENHANCEMENTS ===== */

/* Cursor highlight effect */
.editor.cursor-highlight {
    animation: cursorHighlight 1s ease-out;
    caret-color: rgba(0, 180, 216, 1) !important;
}

@keyframes cursorHighlight {
    0% {
        box-shadow: inset 0 0 0 2px rgba(0, 180, 216, 0.3);
        caret-color: rgba(0, 180, 216, 1);
    }
    50% {
        box-shadow: inset 0 0 0 2px rgba(0, 180, 216, 0.6);
        caret-color: rgba(0, 180, 216, 1);
    }
    100% {
        box-shadow: inset 0 0 0 2px rgba(0, 180, 216, 0);
        caret-color: rgba(0, 180, 216, 0.8);
    }
}

/* Scrolling state indicator */
.editor.is-scrolling {
    transition: all 0.1s ease-out;
}

.editor.is-scrolling::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg,
        rgba(0, 180, 216, 0.8) 0%,
        rgba(144, 224, 239, 0.9) 100%);
    transform: scale(1.2);
    box-shadow: 0 0 10px rgba(0, 180, 216, 0.3);
}

/* Enhanced caret animation */
.editor {
    caret-color: rgba(0, 180, 216, 0.8);
    animation: caretBlink 1.2s ease-in-out infinite;
}

@keyframes caretBlink {
    0%, 50% {
        caret-color: rgba(0, 180, 216, 0.9);
    }
    51%, 100% {
        caret-color: rgba(0, 180, 216, 0.4);
    }
}

/* Smooth focus transition */
.editor:focus {
    caret-color: rgba(0, 180, 216, 1);
    animation: none; /* Stop blinking when focused and typing */
}

/* Light mode cursor highlight */
body.light-mode .editor.cursor-highlight {
    caret-color: rgba(0, 123, 191, 1) !important;
}

@keyframes cursorHighlightLight {
    0% {
        box-shadow: inset 0 0 0 2px rgba(0, 123, 191, 0.3);
        caret-color: rgba(0, 123, 191, 1);
    }
    50% {
        box-shadow: inset 0 0 0 2px rgba(0, 123, 191, 0.6);
        caret-color: rgba(0, 123, 191, 1);
    }
    100% {
        box-shadow: inset 0 0 0 2px rgba(0, 123, 191, 0);
        caret-color: rgba(0, 123, 191, 0.8);
    }
}

body.light-mode .editor.cursor-highlight {
    animation: cursorHighlightLight 1s ease-out;
}

body.light-mode .editor.is-scrolling::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg,
        rgba(0, 123, 191, 0.8) 0%,
        rgba(100, 181, 246, 0.9) 100%);
    box-shadow: 0 0 8px rgba(0, 123, 191, 0.3);
}

/* Auto-scroll indicator */
.editor.auto-scrolling {
    position: relative;
}

.editor.auto-scrolling::after {
    content: '';
    position: absolute;
    top: 10px;
    right: 20px;
    width: 4px;
    height: 20px;
    background: linear-gradient(to bottom,
        rgba(0, 180, 216, 0.8) 0%,
        rgba(0, 180, 216, 0.4) 100%);
    border-radius: 2px;
    animation: scrollIndicator 0.5s ease-out;
    pointer-events: none;
    z-index: 10;
}

@keyframes scrollIndicator {
    0% {
        opacity: 0;
        transform: translateY(-10px);
    }
    50% {
        opacity: 1;
        transform: translateY(0);
    }
    100% {
        opacity: 0;
        transform: translateY(10px);
    }
}

/* Improved scrollbar visibility during typing */
.editor:focus::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg,
        rgba(0, 180, 216, 0.7) 0%,
        rgba(144, 224, 239, 0.8) 100%);
    opacity: 1;
}

.editor:not(:focus)::-webkit-scrollbar-thumb {
    opacity: 0.6;
    transition: opacity 0.3s ease;
}

.editor:not(:focus):hover::-webkit-scrollbar-thumb {
    opacity: 0.8;
}