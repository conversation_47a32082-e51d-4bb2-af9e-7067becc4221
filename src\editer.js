// Backspace Behaviours
editor.addEventListener('keydown', (e) => {
    if (e.key === 'Backspace') {
        const { selectionStart, selectionEnd, value } = e.target;

        if (selectionStart === selectionEnd && selectionStart > 0) {
            const lineStart = value.lastIndexOf('\n', selectionStart - 1) + 1;
            const beforeCursor = value.substring(lineStart, selectionStart);

            // If line only contains spaces and we have 4 spaces before cursor
            if (beforeCursor.match(/^ +$/) && beforeCursor.length >= 4) {
                e.preventDefault();
                const deleteCount = beforeCursor.length % 4 === 0 ? 4 : beforeCursor.length % 4;
                e.target.value = value.slice(0, selectionStart - deleteCount) + value.slice(selectionStart);
                e.target.selectionStart = e.target.selectionEnd = selectionStart - deleteCount;
                return;
            }
        }
    }
});

// Behaviour support
editor.addEventListener('keydown', (e) => {
    if (e.key !== 'Tab') return;

    e.preventDefault();

    const { selectionStart, selectionEnd, value } = e.target;

    if (e.shiftKey) {
        // Shift+Tab:
        const lineStart = value.lastIndexOf('\n', selectionStart - 1) + 1;
        const lineEnd = value.indexOf('\n', selectionEnd);
        const actualLineEnd = lineEnd === -1 ? value.length : lineEnd;

        const selectedText = value.substring(lineStart, actualLineEnd);
        const unindentedText = selectedText.replace(/^    /gm, '');

        e.target.value = value.substring(0, lineStart) + unindentedText + value.substring(actualLineEnd);

        const indentReduction = selectedText.length - unindentedText.length;
        e.target.selectionStart = Math.max(lineStart, selectionStart - Math.min(4, indentReduction));
        e.target.selectionEnd = selectionEnd - indentReduction;
    } else {
        if (selectionStart === selectionEnd) {
            e.target.value = value.slice(0, selectionStart) + '    ' + value.slice(selectionEnd);
            e.target.selectionStart = e.target.selectionEnd = selectionStart + 4;
        } else {
            const lineStart = value.lastIndexOf('\n', selectionStart - 1) + 1;
            const lineEnd = value.indexOf('\n', selectionEnd);
            const actualLineEnd = lineEnd === -1 ? value.length : lineEnd;

            const selectedText = value.substring(lineStart, actualLineEnd);
            const indentedText = selectedText.replace(/^/gm, '    ');

            e.target.value = value.substring(0, lineStart) + indentedText + value.substring(actualLineEnd);
            e.target.selectionStart = selectionStart + 4;
            e.target.selectionEnd = selectionEnd + (indentedText.length - selectedText.length);
        }
    }
});

editor.addEventListener('keydown', (e) => {
    const pairs = { '(': ')', '[': ']', '{': '}', '"': '"', "'": "'" };
    if (!pairs[e.key]) return;

    const { selectionStart, selectionEnd } = e.target;
    if (selectionStart !== selectionEnd) return;

    setTimeout(() => {
        const pos = e.target.selectionStart;
        e.target.value = e.target.value.slice(0, pos) + pairs[e.key] + e.target.value.slice(pos);
        e.target.selectionStart = e.target.selectionEnd = pos;
    }, 0);
});

editor.focus();

// Auto-scroll to cursor position
function scrollToCursor() {
    const editor = document.getElementById('editor');
    if (!editor) return;

    const cursorPosition = editor.selectionStart;
    const textBeforeCursor = editor.value.substring(0, cursorPosition);
    const lines = textBeforeCursor.split('\n');
    const currentLine = lines.length;

    // Calculate approximate line height
    const lineHeight = parseInt(window.getComputedStyle(editor).lineHeight) || 24;
    const cursorTop = (currentLine - 1) * lineHeight;

    // Get editor dimensions
    const editorRect = editor.getBoundingClientRect();
    const scrollTop = editor.scrollTop;
    const visibleHeight = editor.clientHeight;

    // Check if cursor is outside visible area
    const cursorRelativeTop = cursorTop - scrollTop;

    if (cursorRelativeTop < 50) {
        // Cursor is too high, scroll up
        editor.scrollTop = Math.max(0, cursorTop - 50);
    } else if (cursorRelativeTop > visibleHeight - 100) {
        // Cursor is too low, scroll down
        editor.scrollTop = cursorTop - visibleHeight + 100;
    }
}

// Enhanced scroll to cursor with smooth animation
function smoothScrollToCursor() {
    const editor = document.getElementById('editor');
    if (!editor) return;

    const cursorPosition = editor.selectionStart;
    const textBeforeCursor = editor.value.substring(0, cursorPosition);
    const lines = textBeforeCursor.split('\n');
    const currentLine = lines.length;

    // Calculate line height more accurately
    const computedStyle = window.getComputedStyle(editor);
    const lineHeight = parseInt(computedStyle.lineHeight) ||
                      parseInt(computedStyle.fontSize) * 1.7 || 24;

    const cursorTop = (currentLine - 1) * lineHeight;
    const scrollTop = editor.scrollTop;
    const visibleHeight = editor.clientHeight;
    const padding = 80; // Padding from edges

    let targetScrollTop = scrollTop;

    // Calculate if we need to scroll
    const cursorRelativeTop = cursorTop - scrollTop;

    if (cursorRelativeTop < padding) {
        // Cursor is too high
        targetScrollTop = Math.max(0, cursorTop - padding);
    } else if (cursorRelativeTop > visibleHeight - padding) {
        // Cursor is too low
        targetScrollTop = cursorTop - visibleHeight + padding;
    }

    // Smooth scroll animation
    if (targetScrollTop !== scrollTop) {
        const startTime = performance.now();
        const startScrollTop = scrollTop;
        const distance = targetScrollTop - startScrollTop;
        const duration = 300; // Animation duration in ms

        function animateScroll(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // Easing function (ease-out)
            const easeOut = 1 - Math.pow(1 - progress, 3);

            editor.scrollTop = startScrollTop + (distance * easeOut);

            if (progress < 1) {
                requestAnimationFrame(animateScroll);
            }
        }

        requestAnimationFrame(animateScroll);
    }
}

// Add event listeners for auto-scroll
editor.addEventListener('input', () => {
    // Use setTimeout to ensure the cursor position is updated
    setTimeout(smoothScrollToCursor, 0);
});

editor.addEventListener('keydown', (e) => {
    // Handle special keys that move cursor
    const navigationKeys = [
        'ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight',
        'Home', 'End', 'PageUp', 'PageDown',
        'Enter', 'Backspace', 'Delete'
    ];

    if (navigationKeys.includes(e.key)) {
        setTimeout(smoothScrollToCursor, 0);
    }
});

editor.addEventListener('keyup', (e) => {
    // Additional check for cursor movement
    setTimeout(smoothScrollToCursor, 0);
});

editor.addEventListener('click', () => {
    // Scroll to cursor when clicking
    setTimeout(smoothScrollToCursor, 0);
});

editor.addEventListener('paste', () => {
    // Scroll to cursor after paste
    setTimeout(smoothScrollToCursor, 100);
});

// Add cursor highlight effect when scrolling
function highlightCursor() {
    const editor = document.getElementById('editor');
    if (!editor) return;

    // Add highlight class
    editor.classList.add('cursor-highlight');

    // Remove highlight after animation
    setTimeout(() => {
        editor.classList.remove('cursor-highlight');
    }, 1000);
}

// Enhanced smooth scroll with cursor highlight
function smoothScrollToCursorWithHighlight() {
    smoothScrollToCursor();
    highlightCursor();
}

// Update event listeners to use highlighted version for important actions
editor.addEventListener('input', () => {
    const inputLength = editor.value.length;
    // Only highlight for significant input (more than single character)
    if (inputLength > 0) {
        setTimeout(smoothScrollToCursor, 0);
    }
});

// Add scroll position memory for better UX
let lastScrollPosition = 0;
let scrollTimeout = null;

editor.addEventListener('scroll', () => {
    lastScrollPosition = editor.scrollTop;

    // Clear existing timeout
    if (scrollTimeout) {
        clearTimeout(scrollTimeout);
    }

    // Add scrolling class for visual feedback
    editor.classList.add('is-scrolling');

    // Remove scrolling class after scroll ends
    scrollTimeout = setTimeout(() => {
        editor.classList.remove('is-scrolling');
    }, 150);
});

// Auto-scroll to bottom when typing at the end
function autoScrollToEnd() {
    const editor = document.getElementById('editor');
    if (!editor) return;

    const cursorPosition = editor.selectionStart;
    const textLength = editor.value.length;

    // If cursor is at or near the end of text
    if (cursorPosition >= textLength - 10) {
        const maxScroll = editor.scrollHeight - editor.clientHeight;
        if (maxScroll > 0) {
            editor.scrollTop = maxScroll;
        }
    }
}

// Enhanced input handler
editor.addEventListener('input', (e) => {
    // Auto-scroll to end if typing at the end
    setTimeout(() => {
        const cursorPosition = editor.selectionStart;
        const textLength = editor.value.length;

        if (cursorPosition >= textLength - 5) {
            autoScrollToEnd();
        } else {
            smoothScrollToCursor();
        }
    }, 0);
});

// Set placeholder texts
const placeholderText = `Start writing your notes here...
You can:

    - Create a new project
    - Create a new file
    - Save and open files
    - Export files
    - Manage files and projects

Get started.
`;

editor.placeholder = placeholderText;