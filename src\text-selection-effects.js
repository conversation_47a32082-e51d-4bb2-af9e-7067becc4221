/**
 * Enhanced Text Selection Effects
 * Adds smooth animations and interactive effects for text selection
 */

class TextSelectionEffects {
    constructor() {
        this.isSelecting = false;
        this.selectionStartTime = null;
        this.animationFrame = null;
        this.init();
    }

    init() {
        this.setupSelectionEvents();
        this.setupKeyboardShortcuts();
        this.setupCustomCursor();
    }

    setupSelectionEvents() {
        // Track selection start
        document.addEventListener('mousedown', (e) => {
            this.isSelecting = true;
            this.selectionStartTime = Date.now();
            this.addSelectionRipple(e);
        });

        // Track selection end
        document.addEventListener('mouseup', () => {
            if (this.isSelecting) {
                this.isSelecting = false;
                this.handleSelectionComplete();
            }
        });

        // Track selection changes
        document.addEventListener('selectionchange', () => {
            this.handleSelectionChange();
        });

        // Enhanced selection for editor
        const editor = document.getElementById('editor');
        if (editor) {
            editor.addEventListener('select', (e) => {
                this.enhanceEditorSelection(e);
            });
        }
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl+A / Cmd+A - Select all with animation
            if ((e.ctrlKey || e.metaKey) && e.key === 'a') {
                this.animateSelectAll();
            }
            
            // Shift + Arrow keys - Animated selection
            if (e.shiftKey && ['ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'].includes(e.key)) {
                this.animateKeyboardSelection();
            }
        });
    }

    setupCustomCursor() {
        // Add custom cursor effects when hovering over selectable text
        const selectableElements = document.querySelectorAll('p, span, div, textarea, input[type="text"]');
        
        selectableElements.forEach(element => {
            element.addEventListener('mouseenter', () => {
                element.style.cursor = 'text';
            });
        });
    }

    addSelectionRipple(event) {
        const ripple = document.createElement('div');
        ripple.className = 'selection-ripple';
        
        const rect = event.target.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;
        
        ripple.style.cssText = `
            position: absolute;
            left: ${x}px;
            top: ${y}px;
            width: 0;
            height: 0;
            background: radial-gradient(circle, rgba(0, 180, 216, 0.3) 0%, transparent 70%);
            border-radius: 50%;
            pointer-events: none;
            z-index: 1000;
            animation: rippleExpand 0.6s ease-out forwards;
        `;
        
        if (event.target.style.position !== 'absolute' && event.target.style.position !== 'relative') {
            event.target.style.position = 'relative';
        }
        
        event.target.appendChild(ripple);
        
        setTimeout(() => {
            if (ripple.parentNode) {
                ripple.parentNode.removeChild(ripple);
            }
        }, 600);
    }

    handleSelectionComplete() {
        const selection = window.getSelection();
        if (selection.toString().length > 0) {
            this.addSelectionGlow();
            this.showSelectionStats(selection.toString());
        }
    }

    handleSelectionChange() {
        const selection = window.getSelection();
        if (selection.toString().length > 0) {
            this.updateSelectionHighlight();
        }
    }

    enhanceEditorSelection(event) {
        const editor = event.target;
        const start = editor.selectionStart;
        const end = editor.selectionEnd;
        
        if (start !== end) {
            // Add subtle animation to selected text in editor
            editor.style.transition = 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)';
            editor.style.textShadow = '0 0 8px rgba(0, 180, 216, 0.3)';
            
            setTimeout(() => {
                editor.style.textShadow = '';
            }, 1000);
        }
    }

    addSelectionGlow() {
        const selection = window.getSelection();
        if (selection.rangeCount > 0) {
            const range = selection.getRangeAt(0);
            const rect = range.getBoundingClientRect();
            
            const glow = document.createElement('div');
            glow.className = 'selection-glow';
            glow.style.cssText = `
                position: fixed;
                left: ${rect.left - 5}px;
                top: ${rect.top - 5}px;
                width: ${rect.width + 10}px;
                height: ${rect.height + 10}px;
                background: linear-gradient(135deg, 
                    rgba(0, 180, 216, 0.1) 0%, 
                    rgba(144, 224, 239, 0.15) 50%, 
                    rgba(0, 180, 216, 0.1) 100%);
                border-radius: 4px;
                pointer-events: none;
                z-index: -1;
                animation: selectionGlowPulse 2s ease-in-out infinite alternate;
            `;
            
            document.body.appendChild(glow);
            
            setTimeout(() => {
                if (glow.parentNode) {
                    glow.parentNode.removeChild(glow);
                }
            }, 3000);
        }
    }

    showSelectionStats(selectedText) {
        const wordCount = selectedText.trim().split(/\s+/).length;
        const charCount = selectedText.length;
        
        // Create floating stats tooltip
        const tooltip = document.createElement('div');
        tooltip.className = 'selection-stats';
        tooltip.innerHTML = `
            <div class="stats-content">
                <span class="stats-words">${wordCount} words</span>
                <span class="stats-chars">${charCount} chars</span>
            </div>
        `;
        
        tooltip.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: var(--na-overlay-background-darker);
            color: var(--na-text-primary);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            box-shadow: var(--na-shadow-lg);
            backdrop-filter: blur(10px);
            z-index: 1001;
            opacity: 0;
            transform: translateY(20px);
            animation: statsSlideIn 0.3s ease-out forwards;
        `;
        
        document.body.appendChild(tooltip);
        
        setTimeout(() => {
            if (tooltip.parentNode) {
                tooltip.style.animation = 'statsSlideOut 0.3s ease-in forwards';
                setTimeout(() => {
                    if (tooltip.parentNode) {
                        tooltip.parentNode.removeChild(tooltip);
                    }
                }, 300);
            }
        }, 2000);
    }

    updateSelectionHighlight() {
        // Add dynamic highlighting effects
        const selection = window.getSelection();
        if (selection.toString().length > 0) {
            document.body.classList.add('has-selection');
        } else {
            document.body.classList.remove('has-selection');
        }
    }

    animateSelectAll() {
        // Add special animation for select all
        document.body.classList.add('select-all-animation');
        setTimeout(() => {
            document.body.classList.remove('select-all-animation');
        }, 1000);
    }

    animateKeyboardSelection() {
        // Add subtle animation for keyboard selection
        document.body.classList.add('keyboard-selection');
        setTimeout(() => {
            document.body.classList.remove('keyboard-selection');
        }, 200);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new TextSelectionEffects();
});

// Add CSS animations dynamically
const style = document.createElement('style');
style.textContent = `
    @keyframes rippleExpand {
        to {
            width: 100px;
            height: 100px;
            opacity: 0;
        }
    }
    
    @keyframes selectionGlowPulse {
        0% { opacity: 0.1; transform: scale(1); }
        100% { opacity: 0.3; transform: scale(1.02); }
    }
    
    @keyframes statsSlideIn {
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    @keyframes statsSlideOut {
        to {
            opacity: 0;
            transform: translateY(20px);
        }
    }
    
    .has-selection {
        --selection-active: 1;
    }
    
    .select-all-animation * {
        animation: selectAllPulse 0.6s ease-out;
    }
    
    .keyboard-selection ::selection {
        animation: keyboardSelectionPulse 0.2s ease-out;
    }
    
    @keyframes selectAllPulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.005); }
    }
    
    @keyframes keyboardSelectionPulse {
        0%, 100% { opacity: 0.8; }
        50% { opacity: 1; }
    }
    
    .stats-content {
        display: flex;
        gap: 12px;
        align-items: center;
    }
    
    .stats-words, .stats-chars {
        display: flex;
        align-items: center;
        gap: 4px;
    }
    
    .stats-words::before {
        content: "📝";
        font-size: 10px;
    }
    
    .stats-chars::before {
        content: "🔤";
        font-size: 10px;
    }
`;

document.head.appendChild(style);
